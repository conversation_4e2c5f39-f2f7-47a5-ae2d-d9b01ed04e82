دليل التشغيل السريع - نظام أرشفة المستندات
===============================================

تم إنشاء البرنامج بنجاح! إليك خطوات التشغيل:

الخطوة 1: تثبيت .NET 6.0
-------------------------
1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet/6.0
2. حمل "Download .NET 6.0 SDK" (للمطورين)
   أو "Download .NET 6.0 Runtime" (للمستخدمين)
3. قم بتثبيته وأعد تشغيل الكمبيوتر

الخطوة 2: التحقق من التثبيت
---------------------------
1. افتح موجه الأوامر (Command Prompt)
2. اكتب: dotnet --version
3. يجب أن تظهر رسالة مثل: 6.0.x

الخطوة 3: تشغيل البرنامج
------------------------

الطريقة الأولى - تشغيل مباشر:
1. افتح موجه الأوامر في مجلد المشروع
2. اكتب الأوامر التالية واحداً تلو الآخر:
   dotnet restore
   dotnet build
   dotnet run

الطريقة الثانية - استخدام ملف run.bat:
1. انقر مرتين على ملف run.bat
2. انتظر حتى يكتمل التحميل والبناء
3. سيفتح البرنامج تلقائياً

الطريقة الثالثة - بناء ملف تنفيذي:
1. انقر مرتين على ملف build.bat
2. انتظر حتى يكتمل البناء
3. اذهب إلى مجلد publish
4. انقر مرتين على DocumentArchive.exe

استكشاف الأخطاء:
-----------------

خطأ: "dotnet command not found"
الحل: تأكد من تثبيت .NET SDK وأعد تشغيل الكمبيوتر

خطأ: "Unable to restore packages"
الحل: تأكد من اتصال الإنترنت وجرب مرة أخرى

خطأ: "Build failed"
الحل: تأكد من وجود جميع الملفات في المجلد الصحيح

خطأ: "Database error"
الحل: تأكد من وجود صلاحيات الكتابة في مجلد %APPDATA%

ملفات المشروع الموجودة:
------------------------
✓ DocumentArchive.csproj - ملف المشروع
✓ Program.cs - نقطة البداية
✓ Models/ - نماذج البيانات
✓ Data/ - طبقة قاعدة البيانات
✓ Services/ - خدمات التطبيق
✓ Forms/ - واجهات المستخدم
✓ run.bat - ملف التشغيل السريع
✓ build.bat - ملف البناء
✓ README.md - الدليل الشامل

المميزات المتاحة في البرنامج:
-----------------------------
• إضافة مستندات جديدة (وارد/صادر)
• ترقيم تلقائي أو يدوي
• إرفاق ملفات (PDF, Word, Excel, صور)
• البحث والتصفية
• تعديل وحذف المستندات
• تصدير إلى Excel
• واجهة عربية سهلة الاستخدام

للمساعدة:
-----------
راجع ملف README.md للحصول على دليل مفصل
أو ملف "تعليمات_التثبيت.txt" للتعليمات بالعربية

البرنامج جاهز للاستخدام!
========================
