# نظام أرشفة المستندات - سكريبت التشغيل
# Document Archive System - Run Script

Write-Host "==================================" -ForegroundColor Green
Write-Host "نظام أرشفة المستندات" -ForegroundColor Green
Write-Host "Document Archive System" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""

# التحقق من وجود .NET
Write-Host "التحقق من وجود .NET..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم العثور على .NET الإصدار: $dotnetVersion" -ForegroundColor Green
    } else {
        throw "لم يتم العثور على .NET"
    }
} catch {
    Write-Host "✗ خطأ: .NET غير مثبت" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 6.0 من:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "اضغط أي مفتاح للخروج..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host ""

# استعادة الحزم
Write-Host "تحميل المكتبات المطلوبة..." -ForegroundColor Yellow
try {
    dotnet restore
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم تحميل المكتبات بنجاح" -ForegroundColor Green
    } else {
        throw "فشل في تحميل المكتبات"
    }
} catch {
    Write-Host "✗ خطأ في تحميل المكتبات" -ForegroundColor Red
    Write-Host "تأكد من اتصال الإنترنت وجرب مرة أخرى" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "اضغط أي مفتاح للخروج..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host ""

# بناء المشروع
Write-Host "بناء المشروع..." -ForegroundColor Yellow
try {
    dotnet build --configuration Release
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم بناء المشروع بنجاح" -ForegroundColor Green
    } else {
        throw "فشل في بناء المشروع"
    }
} catch {
    Write-Host "✗ خطأ في بناء المشروع" -ForegroundColor Red
    Write-Host "تحقق من وجود جميع الملفات المطلوبة" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "اضغط أي مفتاح للخروج..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host ""

# تشغيل التطبيق
Write-Host "تشغيل نظام أرشفة المستندات..." -ForegroundColor Green
Write-Host "سيتم فتح التطبيق في نافذة منفصلة..." -ForegroundColor Cyan
Write-Host ""

try {
    dotnet run --configuration Release
} catch {
    Write-Host "✗ خطأ في تشغيل التطبيق" -ForegroundColor Red
    Write-Host "تحقق من رسائل الخطأ أعلاه" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "تم إنهاء التطبيق" -ForegroundColor Yellow
Write-Host "اضغط أي مفتاح للخروج..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
