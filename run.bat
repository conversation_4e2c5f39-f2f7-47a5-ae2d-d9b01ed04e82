@echo off
echo تشغيل نظام أرشفة المستندات...
echo.

REM التحقق من وجود .NET
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 6.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo تحميل المكتبات المطلوبة...
dotnet restore

if %errorlevel% neq 0 (
    echo خطأ في تحميل المكتبات
    pause
    exit /b 1
)

echo بناء المشروع...
dotnet build

if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo تشغيل التطبيق...
dotnet run

pause
