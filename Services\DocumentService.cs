using DocumentArchive.Data;
using DocumentArchive.Models;
using OfficeOpenXml;

namespace DocumentArchive.Services
{
    /// <summary>
    /// خدمة المستندات
    /// </summary>
    public class DocumentService
    {
        private readonly DocumentRepository _repository;

        public DocumentService()
        {
            _repository = new DocumentRepository();
        }

        /// <summary>
        /// إنشاء رقم مرجعي جديد
        /// </summary>
        /// <param name="type">نوع المستند</param>
        /// <returns>الرقم المرجعي</returns>
        public string GenerateReferenceNumber(DocumentType type)
        {
            var nextNumber = DatabaseHelper.GetNextDocumentNumber(type);
            var prefix = type == DocumentType.Incoming ? "وارد" : "صادر";
            var year = DateTime.Now.Year;
            
            return $"{prefix}-{nextNumber:D4}-{year}";
        }

        /// <summary>
        /// حفظ مستند جديد
        /// </summary>
        /// <param name="document">المستند</param>
        /// <param name="attachmentFilePath">مسار الملف المرفق (اختياري)</param>
        /// <returns>معرف المستند الجديد</returns>
        public async Task<int> SaveNewDocumentAsync(Document document, string? attachmentFilePath = null)
        {
            // حفظ الملف المرفق إذا كان موجوداً
            if (!string.IsNullOrEmpty(attachmentFilePath) && File.Exists(attachmentFilePath))
            {
                var originalFileName = Path.GetFileName(attachmentFilePath);
                document.AttachmentPath = await FileService.SaveAttachmentAsync(attachmentFilePath, originalFileName);
                document.OriginalFileName = originalFileName;
            }

            // حفظ المستند في قاعدة البيانات
            var documentId = await _repository.AddDocumentAsync(document);

            // تحديث الرقم التالي
            if (document.ReferenceNumber.StartsWith("وارد") || document.ReferenceNumber.StartsWith("صادر"))
            {
                var nextNumber = DatabaseHelper.GetNextDocumentNumber(document.Type) + 1;
                DatabaseHelper.UpdateNextDocumentNumber(document.Type, nextNumber);
            }

            return documentId;
        }

        /// <summary>
        /// تحديث مستند موجود
        /// </summary>
        /// <param name="document">المستند</param>
        /// <param name="newAttachmentFilePath">مسار الملف المرفق الجديد (اختياري)</param>
        public async Task UpdateDocumentAsync(Document document, string? newAttachmentFilePath = null)
        {
            // الحصول على المستند الحالي لمقارنة المرفقات
            var existingDocument = await _repository.GetDocumentByIdAsync(document.Id);

            // إدارة الملف المرفق الجديد
            if (!string.IsNullOrEmpty(newAttachmentFilePath) && File.Exists(newAttachmentFilePath))
            {
                // حذف الملف المرفق القديم إذا كان موجوداً
                if (existingDocument?.AttachmentPath != null)
                {
                    FileService.DeleteAttachment(existingDocument.AttachmentPath);
                }

                // حفظ الملف الجديد
                var originalFileName = Path.GetFileName(newAttachmentFilePath);
                document.AttachmentPath = await FileService.SaveAttachmentAsync(newAttachmentFilePath, originalFileName);
                document.OriginalFileName = originalFileName;
            }
            else if (existingDocument != null)
            {
                // الاحتفاظ بالملف المرفق الحالي
                document.AttachmentPath = existingDocument.AttachmentPath;
                document.OriginalFileName = existingDocument.OriginalFileName;
            }

            // تحديث المستند في قاعدة البيانات
            await _repository.UpdateDocumentAsync(document);
        }

        /// <summary>
        /// حذف مستند
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        public async Task DeleteDocumentAsync(int documentId)
        {
            // الحصول على المستند لحذف الملف المرفق
            var document = await _repository.GetDocumentByIdAsync(documentId);
            
            if (document?.AttachmentPath != null)
            {
                FileService.DeleteAttachment(document.AttachmentPath);
            }

            // حذف المستند من قاعدة البيانات
            await _repository.DeleteDocumentAsync(documentId);
        }

        /// <summary>
        /// تصدير المستندات إلى Excel
        /// </summary>
        /// <param name="documents">قائمة المستندات</param>
        /// <param name="filePath">مسار ملف Excel</param>
        public async Task ExportToExcelAsync(List<Document> documents, string filePath)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المستندات");

            // إعداد العناوين
            var headers = new[]
            {
                "الرقم المرجعي", "النوع", "التاريخ", "المرسل/المستلم", 
                "الجهة المرتبطة", "الموضوع", "الملاحظات", "يوجد مرفق"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // إضافة البيانات
            for (int i = 0; i < documents.Count; i++)
            {
                var doc = documents[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = doc.ReferenceNumber;
                worksheet.Cells[row, 2].Value = doc.TypeDisplayText;
                worksheet.Cells[row, 3].Value = doc.DocumentDate.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 4].Value = doc.SenderReceiver;
                worksheet.Cells[row, 5].Value = doc.RelatedEntity;
                worksheet.Cells[row, 6].Value = doc.Subject;
                worksheet.Cells[row, 7].Value = doc.Notes;
                worksheet.Cells[row, 8].Value = doc.HasAttachment ? "نعم" : "لا";
            }

            // تنسيق الجدول
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            // حفظ الملف
            await package.SaveAsAsync(new FileInfo(filePath));
        }

        /// <summary>
        /// الحصول على إحصائيات المستندات
        /// </summary>
        /// <returns>إحصائيات المستندات</returns>
        public async Task<DocumentStatistics> GetDocumentStatisticsAsync()
        {
            var allDocuments = await _repository.GetAllDocumentsAsync();
            
            return new DocumentStatistics
            {
                TotalDocuments = allDocuments.Count,
                IncomingDocuments = allDocuments.Count(d => d.Type == DocumentType.Incoming),
                OutgoingDocuments = allDocuments.Count(d => d.Type == DocumentType.Outgoing),
                DocumentsWithAttachments = allDocuments.Count(d => d.HasAttachment),
                DocumentsThisMonth = allDocuments.Count(d => d.DocumentDate.Month == DateTime.Now.Month && 
                                                           d.DocumentDate.Year == DateTime.Now.Year),
                DocumentsThisYear = allDocuments.Count(d => d.DocumentDate.Year == DateTime.Now.Year)
            };
        }
    }

    /// <summary>
    /// إحصائيات المستندات
    /// </summary>
    public class DocumentStatistics
    {
        public int TotalDocuments { get; set; }
        public int IncomingDocuments { get; set; }
        public int OutgoingDocuments { get; set; }
        public int DocumentsWithAttachments { get; set; }
        public int DocumentsThisMonth { get; set; }
        public int DocumentsThisYear { get; set; }
    }
}
