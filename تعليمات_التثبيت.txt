نظام أرشفة المستندات - تعليمات التثبيت والتشغيل
=================================================

المتطلبات:
----------
1. نظام التشغيل: Windows 10 أو أحدث
2. .NET 6.0 SDK أو Runtime

خطوات التثبيت:
--------------

الطريقة الأولى - تشغيل مباشر (للمطورين):
1. تثبيت .NET 6.0 SDK من:
   https://dotnet.microsoft.com/download/dotnet/6.0

2. فتح موجه الأوامر في مجلد المشروع

3. تشغيل الأمر: run.bat
   أو تشغيل الأوامر التالية:
   dotnet restore
   dotnet build
   dotnet run

الطريقة الثانية - بناء ملف تنفيذي:
1. تثبيت .NET 6.0 SDK

2. تشغيل: build.bat
   أو تشغيل الأمر:
   dotnet publish -c Release -r win-x64 --self-contained -o ./publish

3. ستجد الملف التنفيذي في مجلد publish
   اسم الملف: DocumentArchive.exe

4. يمكنك نسخ مجلد publish إلى أي مكان وتشغيل البرنامج

الطريقة الثالثة - للمستخدمين النهائيين:
1. تثبيت .NET 6.0 Runtime فقط من:
   https://dotnet.microsoft.com/download/dotnet/6.0

2. الحصول على الملف التنفيذي الجاهز

3. تشغيل DocumentArchive.exe

ملاحظات مهمة:
--------------
- سيتم إنشاء قاعدة البيانات تلقائياً في المجلد:
  %APPDATA%\DocumentArchive\

- سيتم حفظ الملفات المرفقة في:
  %APPDATA%\DocumentArchive\Attachments\

- لا يحتاج البرنامج إلى صلاحيات مدير النظام

- يمكن تشغيل البرنامج من أي مجلد

استكشاف الأخطاء:
-----------------
1. إذا ظهرت رسالة خطأ حول .NET:
   - تأكد من تثبيت .NET 6.0
   - أعد تشغيل الكمبيوتر بعد التثبيت

2. إذا لم يتم إنشاء قاعدة البيانات:
   - تأكد من وجود صلاحيات الكتابة في مجلد %APPDATA%

3. إذا لم تعمل المرفقات:
   - تأكد من وجود مساحة كافية على القرص الصلب

4. للحصول على مساعدة إضافية:
   - راجع ملف README.md
   - تحقق من رسائل الخطأ في التطبيق

الدعم:
-------
للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

تاريخ الإصدار: 2024
الإصدار: 1.0.0
