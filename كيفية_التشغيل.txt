🚀 كيفية تشغيل نظام أرشفة المستندات
=====================================

تم إنشاء البرنامج بنجاح! اتبع هذه الخطوات لتشغيله:

📋 المتطلبات الأساسية:
----------------------
✓ Windows 10 أو أحدث
✓ .NET 6.0 SDK أو Runtime
✓ 100 ميجابايت مساحة فارغة

🔧 الخطوة 1: تثبيت .NET 6.0
---------------------------
1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet/6.0

2. اختر أحد الخيارات:
   • للمطورين: "Download .NET 6.0 SDK"
   • للمستخدمين: "Download .NET 6.0 Runtime"

3. قم بتثبيت البرنامج وأعد تشغيل الكمبيوتر

4. للتحقق من التثبيت:
   - افتح موجه الأوامر (cmd)
   - اكتب: dotnet --version
   - يجب أن تظهر رسالة مثل: 6.0.x

🎯 الخطوة 2: تشغيل البرنامج
---------------------------

🟢 الطريقة الأسهل:
انقر مرتين على أحد الملفات التالية:
• start.cmd (الأفضل)
• run.bat
• run.ps1 (PowerShell)

🟡 الطريقة اليدوية:
1. افتح موجه الأوامر في مجلد المشروع
2. اكتب الأوامر التالية:
   dotnet restore
   dotnet build
   dotnet run

🔵 بناء ملف تنفيذي مستقل:
1. انقر مرتين على build.bat
2. انتظر حتى يكتمل البناء
3. اذهب إلى مجلد "publish"
4. انقر مرتين على "DocumentArchive.exe"

📁 ملفات التشغيل المتاحة:
--------------------------
• start.cmd - الأفضل للاستخدام العادي
• run.bat - تشغيل بسيط
• run.ps1 - PowerShell script
• build.bat - بناء ملف تنفيذي

🛠️ استكشاف الأخطاء:
-------------------

❌ خطأ: "dotnet command not found"
✅ الحل: تثبيت .NET SDK وإعادة تشغيل الكمبيوتر

❌ خطأ: "Unable to restore packages"
✅ الحل: التحقق من اتصال الإنترنت

❌ خطأ: "Build failed"
✅ الحل: التأكد من وجود جميع الملفات

❌ خطأ: "Access denied"
✅ الحل: تشغيل موجه الأوامر كمدير

❌ خطأ: "Database error"
✅ الحل: التحقق من صلاحيات الكتابة في %APPDATA%

🎮 استخدام البرنامج:
--------------------

عند تشغيل البرنامج ستظهر الشاشة الرئيسية مع:

📝 شريط الأدوات:
• مستند جديد - لإضافة مستند
• تعديل - لتعديل المستند المحدد
• حذف - لحذف المستند المحدد
• تحديث - لتحديث قائمة المستندات
• تصدير إلى Excel - لحفظ البيانات

🔍 شريط البحث:
• مربع البحث - للبحث في جميع الحقول
• نوع المستند - فلتر حسب وارد/صادر
• زر البحث - تطبيق البحث
• زر مسح - إلغاء البحث

📊 جدول المستندات:
• انقر مرتين على أي مستند لتعديله
• اضغط Delete لحذف المستند المحدد
• اضغط Enter لتعديل المستند المحدد

💾 حفظ البيانات:
----------------
• قاعدة البيانات: %APPDATA%\DocumentArchive\documents.db
• المرفقات: %APPDATA%\DocumentArchive\Attachments\
• لا يحتاج إنترنت للعمل
• نسخ احتياطية تلقائية

📞 الدعم الفني:
---------------
• راجع ملف README.md للدليل الشامل
• تحقق من رسائل الخطأ في التطبيق
• تأكد من تحديث .NET إلى أحدث إصدار

🎉 البرنامج جاهز للاستخدام!
============================

ملاحظة: عند التشغيل لأول مرة، سيتم إنشاء قاعدة البيانات
ومجلدات التخزين تلقائياً.
