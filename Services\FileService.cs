namespace DocumentArchive.Services
{
    /// <summary>
    /// خدمة إدارة الملفات
    /// </summary>
    public class FileService
    {
        private static readonly string AttachmentsFolder = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DocumentArchive",
            "Attachments"
        );

        /// <summary>
        /// مجلد المرفقات
        /// </summary>
        public static string AttachmentsFolderPath => AttachmentsFolder;

        /// <summary>
        /// تهيئة مجلد المرفقات
        /// </summary>
        public static void InitializeAttachmentsFolder()
        {
            if (!Directory.Exists(AttachmentsFolder))
            {
                Directory.CreateDirectory(AttachmentsFolder);
            }
        }

        /// <summary>
        /// حفظ ملف مرفق
        /// </summary>
        /// <param name="sourceFilePath">مسار الملف المصدر</param>
        /// <param name="originalFileName">اسم الملف الأصلي</param>
        /// <returns>مسار الملف المحفوظ</returns>
        public static async Task<string> SaveAttachmentAsync(string sourceFilePath, string originalFileName)
        {
            InitializeAttachmentsFolder();

            // إنشاء اسم ملف فريد
            var fileExtension = Path.GetExtension(originalFileName);
            var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
            var destinationPath = Path.Combine(AttachmentsFolder, uniqueFileName);

            // نسخ الملف
            await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));

            return destinationPath;
        }

        /// <summary>
        /// حذف ملف مرفق
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public static void DeleteAttachment(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ أو إظهار رسالة للمستخدم
                MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// فتح ملف مرفق
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public static void OpenAttachment(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التحقق من صحة امتداد الملف
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>true إذا كان الامتداد مدعوماً</returns>
        public static bool IsValidFileExtension(string fileName)
        {
            var allowedExtensions = new[]
            {
                ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                ".txt", ".rtf", ".jpg", ".jpeg", ".png", ".gif", ".bmp",
                ".tiff", ".zip", ".rar", ".7z"
            };

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return allowedExtensions.Contains(extension);
        }

        /// <summary>
        /// الحصول على حجم الملف بصيغة قابلة للقراءة
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>حجم الملف</returns>
        public static string GetFileSize(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "غير معروف";

                var fileInfo = new FileInfo(filePath);
                var sizeInBytes = fileInfo.Length;

                if (sizeInBytes < 1024)
                    return $"{sizeInBytes} بايت";
                else if (sizeInBytes < 1024 * 1024)
                    return $"{sizeInBytes / 1024:F1} كيلوبايت";
                else if (sizeInBytes < 1024 * 1024 * 1024)
                    return $"{sizeInBytes / (1024 * 1024):F1} ميجابايت";
                else
                    return $"{sizeInBytes / (1024 * 1024 * 1024):F1} جيجابايت";
            }
            catch
            {
                return "غير معروف";
            }
        }

        /// <summary>
        /// الحصول على أيقونة الملف حسب الامتداد
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>نص يمثل نوع الملف</returns>
        public static string GetFileTypeIcon(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            
            return extension switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" => "🖼️",
                ".zip" or ".rar" or ".7z" => "📦",
                ".txt" => "📃",
                _ => "📎"
            };
        }
    }
}
