using DocumentArchive.Models;
using DocumentArchive.Services;

namespace DocumentArchive.Forms
{
    public partial class AddEditDocumentForm : Form
    {
        private readonly DocumentService _documentService;
        private readonly Document? _existingDocument;
        private string? _selectedAttachmentPath;

        // Controls
        private TextBox txtReferenceNumber = null!;
        private ComboBox cmbType = null!;
        private DateTimePicker dtpDocumentDate = null!;
        private TextBox txtSenderReceiver = null!;
        private TextBox txtRelatedEntity = null!;
        private TextBox txtSubject = null!;
        private TextBox txtNotes = null!;
        private Label lblAttachment = null!;
        private Button btnBrowseAttachment = null!;
        private Button btnRemoveAttachment = null!;
        private Button btnViewAttachment = null!;
        private Button btnSave = null!;
        private Button btnCancel = null!;
        private CheckBox chkAutoReference = null!;

        public AddEditDocumentForm(Document? document = null)
        {
            _documentService = new DocumentService();
            _existingDocument = document;

            SetupForm();
            LoadData();
        }

        private void SetupForm()
        {
            Text = _existingDocument == null ? "إضافة مستند جديد" : "تعديل المستند";
            Size = new Size(600, 500);
            StartPosition = FormStartPosition.CenterParent;
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // الرقم المرجعي
            txtReferenceNumber = new TextBox { Width = 200 };
            chkAutoReference = new CheckBox 
            { 
                Text = "ترقيم تلقائي", 
                Checked = _existingDocument == null,
                Width = 100
            };
            chkAutoReference.CheckedChanged += ChkAutoReference_CheckedChanged;

            // نوع المستند
            cmbType = new ComboBox 
            { 
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200
            };
            cmbType.Items.Add("وارد");
            cmbType.Items.Add("صادر");
            cmbType.SelectedIndexChanged += CmbType_SelectedIndexChanged;

            // تاريخ المستند
            dtpDocumentDate = new DateTimePicker 
            { 
                Format = DateTimePickerFormat.Short,
                Width = 200
            };

            // المرسل/المستلم
            txtSenderReceiver = new TextBox { Width = 400 };

            // الجهة المرتبطة
            txtRelatedEntity = new TextBox { Width = 400 };

            // الموضوع
            txtSubject = new TextBox { Width = 400 };

            // الملاحظات
            txtNotes = new TextBox 
            { 
                Width = 400, 
                Height = 80, 
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // المرفقات
            lblAttachment = new Label 
            { 
                Text = "لا يوجد ملف مرفق",
                Width = 300,
                AutoEllipsis = true
            };

            btnBrowseAttachment = new Button 
            { 
                Text = "استعراض...",
                Width = 80
            };
            btnBrowseAttachment.Click += BtnBrowseAttachment_Click;

            btnRemoveAttachment = new Button 
            { 
                Text = "إزالة",
                Width = 60,
                Enabled = false
            };
            btnRemoveAttachment.Click += BtnRemoveAttachment_Click;

            btnViewAttachment = new Button 
            { 
                Text = "عرض",
                Width = 60,
                Enabled = false
            };
            btnViewAttachment.Click += BtnViewAttachment_Click;

            // أزرار الحفظ والإلغاء
            btnSave = new Button 
            { 
                Text = "حفظ",
                Width = 80,
                DialogResult = DialogResult.OK
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button 
            { 
                Text = "إلغاء",
                Width = 80,
                DialogResult = DialogResult.Cancel
            };
        }

        private void LayoutControls()
        {
            var y = 20;
            const int labelWidth = 120;
            const int spacing = 35;

            // الرقم المرجعي
            Controls.Add(new Label { Text = "الرقم المرجعي:", Location = new Point(450, y), Width = labelWidth });
            txtReferenceNumber.Location = new Point(240, y);
            chkAutoReference.Location = new Point(130, y);
            Controls.AddRange(new Control[] { txtReferenceNumber, chkAutoReference });
            y += spacing;

            // نوع المستند
            Controls.Add(new Label { Text = "نوع المستند:", Location = new Point(450, y), Width = labelWidth });
            cmbType.Location = new Point(240, y);
            Controls.Add(cmbType);
            y += spacing;

            // تاريخ المستند
            Controls.Add(new Label { Text = "تاريخ المستند:", Location = new Point(450, y), Width = labelWidth });
            dtpDocumentDate.Location = new Point(240, y);
            Controls.Add(dtpDocumentDate);
            y += spacing;

            // المرسل/المستلم
            Controls.Add(new Label { Text = "المرسل/المستلم:", Location = new Point(450, y), Width = labelWidth });
            txtSenderReceiver.Location = new Point(40, y);
            Controls.Add(txtSenderReceiver);
            y += spacing;

            // الجهة المرتبطة
            Controls.Add(new Label { Text = "الجهة المرتبطة:", Location = new Point(450, y), Width = labelWidth });
            txtRelatedEntity.Location = new Point(40, y);
            Controls.Add(txtRelatedEntity);
            y += spacing;

            // الموضوع
            Controls.Add(new Label { Text = "الموضوع:", Location = new Point(450, y), Width = labelWidth });
            txtSubject.Location = new Point(40, y);
            Controls.Add(txtSubject);
            y += spacing;

            // الملاحظات
            Controls.Add(new Label { Text = "الملاحظات:", Location = new Point(450, y), Width = labelWidth });
            txtNotes.Location = new Point(40, y);
            Controls.Add(txtNotes);
            y += 90;

            // المرفقات
            Controls.Add(new Label { Text = "الملف المرفق:", Location = new Point(450, y), Width = labelWidth });
            lblAttachment.Location = new Point(40, y + 3);
            btnBrowseAttachment.Location = new Point(350, y);
            btnRemoveAttachment.Location = new Point(280, y);
            btnViewAttachment.Location = new Point(210, y);
            Controls.AddRange(new Control[] { lblAttachment, btnBrowseAttachment, btnRemoveAttachment, btnViewAttachment });
            y += 50;

            // أزرار الحفظ والإلغاء
            btnSave.Location = new Point(350, y);
            btnCancel.Location = new Point(260, y);
            Controls.AddRange(new Control[] { btnSave, btnCancel });
        }

        private void LoadData()
        {
            if (_existingDocument != null)
            {
                txtReferenceNumber.Text = _existingDocument.ReferenceNumber;
                cmbType.SelectedIndex = _existingDocument.Type == DocumentType.Incoming ? 0 : 1;
                dtpDocumentDate.Value = _existingDocument.DocumentDate;
                txtSenderReceiver.Text = _existingDocument.SenderReceiver;
                txtRelatedEntity.Text = _existingDocument.RelatedEntity;
                txtSubject.Text = _existingDocument.Subject;
                txtNotes.Text = _existingDocument.Notes;

                if (_existingDocument.HasAttachment)
                {
                    lblAttachment.Text = _existingDocument.OriginalFileName ?? "ملف مرفق";
                    btnRemoveAttachment.Enabled = true;
                    btnViewAttachment.Enabled = true;
                }

                chkAutoReference.Checked = false;
                txtReferenceNumber.ReadOnly = false;
            }
            else
            {
                cmbType.SelectedIndex = 0;
                dtpDocumentDate.Value = DateTime.Now;
                UpdateReferenceNumber();
            }
        }

        private void ChkAutoReference_CheckedChanged(object? sender, EventArgs e)
        {
            txtReferenceNumber.ReadOnly = chkAutoReference.Checked;
            if (chkAutoReference.Checked)
            {
                UpdateReferenceNumber();
            }
        }

        private void CmbType_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (chkAutoReference.Checked)
            {
                UpdateReferenceNumber();
            }
        }

        private void UpdateReferenceNumber()
        {
            if (chkAutoReference.Checked && cmbType.SelectedIndex >= 0)
            {
                var type = cmbType.SelectedIndex == 0 ? DocumentType.Incoming : DocumentType.Outgoing;
                txtReferenceNumber.Text = _documentService.GenerateReferenceNumber(type);
            }
        }

        private void BtnBrowseAttachment_Click(object? sender, EventArgs e)
        {
            using var openDialog = new OpenFileDialog
            {
                Title = "اختيار ملف مرفق",
                Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pptx;*.txt;*.jpg;*.jpeg;*.png;*.gif;*.bmp|" +
                        "ملفات PDF|*.pdf|" +
                        "مستندات Word|*.doc;*.docx|" +
                        "جداول Excel|*.xls;*.xlsx|" +
                        "عروض PowerPoint|*.ppt;*.pptx|" +
                        "ملفات نصية|*.txt|" +
                        "صور|*.jpg;*.jpeg;*.png;*.gif;*.bmp|" +
                        "جميع الملفات|*.*"
            };

            if (openDialog.ShowDialog() == DialogResult.OK)
            {
                if (FileService.IsValidFileExtension(openDialog.FileName))
                {
                    _selectedAttachmentPath = openDialog.FileName;
                    lblAttachment.Text = Path.GetFileName(openDialog.FileName);
                    btnRemoveAttachment.Enabled = true;
                    btnViewAttachment.Enabled = true;
                }
                else
                {
                    MessageBox.Show("نوع الملف غير مدعوم", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        private void BtnRemoveAttachment_Click(object? sender, EventArgs e)
        {
            _selectedAttachmentPath = null;
            lblAttachment.Text = "لا يوجد ملف مرفق";
            btnRemoveAttachment.Enabled = false;
            btnViewAttachment.Enabled = false;
        }

        private void BtnViewAttachment_Click(object? sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(_selectedAttachmentPath))
            {
                FileService.OpenAttachment(_selectedAttachmentPath);
            }
            else if (_existingDocument?.AttachmentPath != null)
            {
                FileService.OpenAttachment(_existingDocument.AttachmentPath);
            }
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var document = CreateDocumentFromInput();

                if (_existingDocument == null)
                {
                    await _documentService.SaveNewDocumentAsync(document, _selectedAttachmentPath);
                }
                else
                {
                    document.Id = _existingDocument.Id;
                    document.CreatedDate = _existingDocument.CreatedDate;
                    await _documentService.UpdateDocumentAsync(document, _selectedAttachmentPath);
                }

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المستند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtReferenceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال الرقم المرجعي", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReferenceNumber.Focus();
                return false;
            }

            if (cmbType.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار نوع المستند", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbType.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSenderReceiver.Text))
            {
                MessageBox.Show("يرجى إدخال المرسل/المستلم", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSenderReceiver.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSubject.Text))
            {
                MessageBox.Show("يرجى إدخال موضوع المستند", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSubject.Focus();
                return false;
            }

            return true;
        }

        private Document CreateDocumentFromInput()
        {
            return new Document
            {
                ReferenceNumber = txtReferenceNumber.Text.Trim(),
                Type = cmbType.SelectedIndex == 0 ? DocumentType.Incoming : DocumentType.Outgoing,
                DocumentDate = dtpDocumentDate.Value.Date,
                SenderReceiver = txtSenderReceiver.Text.Trim(),
                RelatedEntity = txtRelatedEntity.Text.Trim(),
                Subject = txtSubject.Text.Trim(),
                Notes = txtNotes.Text.Trim()
            };
        }
    }
}
