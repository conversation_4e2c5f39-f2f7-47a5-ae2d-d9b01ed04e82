@echo off
chcp 65001 >nul
title نظام أرشفة المستندات

echo.
echo ========================================
echo        نظام أرشفة المستندات
echo        Document Archive System
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود .NET
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo [خطأ] لم يتم العثور على .NET
    echo.
    echo يرجى تثبيت .NET 6.0 من الرابط التالي:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo اختر "Download .NET 6.0 SDK" للمطورين
    echo أو "Download .NET 6.0 Runtime" للمستخدمين
    echo.
    pause
    exit /b 1
)

echo [✓] تم العثور على .NET
echo.

echo جاري تحميل المكتبات المطلوبة...
dotnet restore --verbosity quiet
if %errorlevel% neq 0 (
    echo [خطأ] فشل في تحميل المكتبات
    echo تأكد من اتصال الإنترنت
    pause
    exit /b 1
)

echo [✓] تم تحميل المكتبات
echo.

echo جاري بناء المشروع...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo [خطأ] فشل في بناء المشروع
    pause
    exit /b 1
)

echo [✓] تم بناء المشروع
echo.

echo جاري تشغيل نظام أرشفة المستندات...
echo سيتم فتح التطبيق في نافذة منفصلة...
echo.
echo لإغلاق هذه النافذة، أغلق التطبيق أولاً
echo.

dotnet run --configuration Release

echo.
echo تم إنهاء التطبيق
pause
