@echo off
echo بناء نظام أرشفة المستندات...
echo.

REM التحقق من وجود .NET
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 6.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo تحميل المكتبات المطلوبة...
dotnet restore

if %errorlevel% neq 0 (
    echo خطأ في تحميل المكتبات
    pause
    exit /b 1
)

echo بناء التطبيق للنشر...
dotnet publish -c Release -r win-x64 --self-contained -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -o ./publish

if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo تم بناء التطبيق بنجاح!
echo يمكنك العثور على الملف التنفيذي في مجلد: publish
echo اسم الملف: DocumentArchive.exe
echo.

pause
