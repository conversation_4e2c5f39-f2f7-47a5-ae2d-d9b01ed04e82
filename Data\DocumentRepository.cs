using System.Data.SQLite;
using DocumentArchive.Models;

namespace DocumentArchive.Data
{
    /// <summary>
    /// مستودع المستندات
    /// </summary>
    public class DocumentRepository
    {
        /// <summary>
        /// إضافة مستند جديد
        /// </summary>
        /// <param name="document">المستند</param>
        /// <returns>معرف المستند الجديد</returns>
        public async Task<int> AddDocumentAsync(Document document)
        {
            using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
            await connection.OpenAsync();

            var sql = @"
                INSERT INTO Documents (
                    ReferenceNumber, Type, DocumentDate, SenderReceiver, 
                    RelatedEntity, Subject, Notes, AttachmentPath, 
                    OriginalFileName, CreatedDate, ModifiedDate
                ) VALUES (
                    @ReferenceNumber, @Type, @DocumentDate, @SenderReceiver,
                    @RelatedEntity, @Subject, @Notes, @AttachmentPath,
                    @OriginalFileName, @CreatedDate, @ModifiedDate
                );
                SELECT last_insert_rowid();
            ";

            using var command = new SQLiteCommand(sql, connection);
            AddDocumentParameters(command, document);

            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// تحديث مستند موجود
        /// </summary>
        /// <param name="document">المستند</param>
        public async Task UpdateDocumentAsync(Document document)
        {
            using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
            await connection.OpenAsync();

            var sql = @"
                UPDATE Documents SET
                    ReferenceNumber = @ReferenceNumber,
                    Type = @Type,
                    DocumentDate = @DocumentDate,
                    SenderReceiver = @SenderReceiver,
                    RelatedEntity = @RelatedEntity,
                    Subject = @Subject,
                    Notes = @Notes,
                    AttachmentPath = @AttachmentPath,
                    OriginalFileName = @OriginalFileName,
                    ModifiedDate = @ModifiedDate
                WHERE Id = @Id
            ";

            using var command = new SQLiteCommand(sql, connection);
            AddDocumentParameters(command, document);
            command.Parameters.AddWithValue("@Id", document.Id);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// حذف مستند
        /// </summary>
        /// <param name="id">معرف المستند</param>
        public async Task DeleteDocumentAsync(int id)
        {
            using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
            await connection.OpenAsync();

            using var command = new SQLiteCommand("DELETE FROM Documents WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// الحصول على مستند بالمعرف
        /// </summary>
        /// <param name="id">معرف المستند</param>
        /// <returns>المستند أو null</returns>
        public async Task<Document?> GetDocumentByIdAsync(int id)
        {
            using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
            await connection.OpenAsync();

            using var command = new SQLiteCommand("SELECT * FROM Documents WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);

            using var reader = await command.ExecuteReaderAsync();
            return reader.Read() ? MapReaderToDocument(reader) : null;
        }

        /// <summary>
        /// الحصول على جميع المستندات
        /// </summary>
        /// <returns>قائمة المستندات</returns>
        public async Task<List<Document>> GetAllDocumentsAsync()
        {
            using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
            await connection.OpenAsync();

            using var command = new SQLiteCommand("SELECT * FROM Documents ORDER BY DocumentDate DESC", connection);
            using var reader = await command.ExecuteReaderAsync();

            var documents = new List<Document>();
            while (reader.Read())
            {
                documents.Add(MapReaderToDocument(reader));
            }

            return documents;
        }

        /// <summary>
        /// البحث في المستندات
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="type">نوع المستند (اختياري)</param>
        /// <param name="dateFrom">من تاريخ (اختياري)</param>
        /// <param name="dateTo">إلى تاريخ (اختياري)</param>
        /// <returns>قائمة المستندات المطابقة</returns>
        public async Task<List<Document>> SearchDocumentsAsync(string? searchTerm = null, 
            DocumentType? type = null, DateTime? dateFrom = null, DateTime? dateTo = null)
        {
            using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
            await connection.OpenAsync();

            var sql = "SELECT * FROM Documents WHERE 1=1";
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                sql += " AND (ReferenceNumber LIKE @search OR SenderReceiver LIKE @search OR Subject LIKE @search OR Notes LIKE @search)";
                parameters.Add(new SQLiteParameter("@search", $"%{searchTerm}%"));
            }

            if (type.HasValue)
            {
                sql += " AND Type = @type";
                parameters.Add(new SQLiteParameter("@type", (int)type.Value));
            }

            if (dateFrom.HasValue)
            {
                sql += " AND DocumentDate >= @dateFrom";
                parameters.Add(new SQLiteParameter("@dateFrom", dateFrom.Value.ToString("yyyy-MM-dd")));
            }

            if (dateTo.HasValue)
            {
                sql += " AND DocumentDate <= @dateTo";
                parameters.Add(new SQLiteParameter("@dateTo", dateTo.Value.ToString("yyyy-MM-dd")));
            }

            sql += " ORDER BY DocumentDate DESC";

            using var command = new SQLiteCommand(sql, connection);
            foreach (var param in parameters)
            {
                command.Parameters.Add(param);
            }

            using var reader = await command.ExecuteReaderAsync();
            var documents = new List<Document>();
            while (reader.Read())
            {
                documents.Add(MapReaderToDocument(reader));
            }

            return documents;
        }

        /// <summary>
        /// إضافة معاملات المستند للأمر
        /// </summary>
        private static void AddDocumentParameters(SQLiteCommand command, Document document)
        {
            command.Parameters.AddWithValue("@ReferenceNumber", document.ReferenceNumber);
            command.Parameters.AddWithValue("@Type", (int)document.Type);
            command.Parameters.AddWithValue("@DocumentDate", document.DocumentDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@SenderReceiver", document.SenderReceiver);
            command.Parameters.AddWithValue("@RelatedEntity", document.RelatedEntity);
            command.Parameters.AddWithValue("@Subject", document.Subject);
            command.Parameters.AddWithValue("@Notes", document.Notes ?? string.Empty);
            command.Parameters.AddWithValue("@AttachmentPath", document.AttachmentPath ?? string.Empty);
            command.Parameters.AddWithValue("@OriginalFileName", document.OriginalFileName ?? string.Empty);
            command.Parameters.AddWithValue("@CreatedDate", document.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى مستند
        /// </summary>
        private static Document MapReaderToDocument(SQLiteDataReader reader)
        {
            return new Document
            {
                Id = reader.GetInt32("Id"),
                ReferenceNumber = reader.GetString("ReferenceNumber"),
                Type = (DocumentType)reader.GetInt32("Type"),
                DocumentDate = DateTime.Parse(reader.GetString("DocumentDate")),
                SenderReceiver = reader.GetString("SenderReceiver"),
                RelatedEntity = reader.GetString("RelatedEntity"),
                Subject = reader.GetString("Subject"),
                Notes = reader.GetString("Notes"),
                AttachmentPath = reader.IsDBNull("AttachmentPath") ? null : reader.GetString("AttachmentPath"),
                OriginalFileName = reader.IsDBNull("OriginalFileName") ? null : reader.GetString("OriginalFileName"),
                CreatedDate = DateTime.Parse(reader.GetString("CreatedDate")),
                ModifiedDate = DateTime.Parse(reader.GetString("ModifiedDate"))
            };
        }
    }
}
