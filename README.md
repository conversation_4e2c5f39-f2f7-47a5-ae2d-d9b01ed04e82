# نظام أرشفة المستندات

نظام بسيط وفعال لأرشفة المستندات الصادرة والواردة للمؤسسات الصغيرة والمتوسطة.

## المميزات الرئيسية

### ✅ إدارة المستندات
- تصنيف المستندات كـ "واردة" أو "صادرة"
- نظام ترقيم تلقائي للمستندات
- إمكانية الترقيم اليدوي عند الحاجة
- حفظ تاريخ المستند مع إمكانية التعديل

### ✅ معلومات المستند
- الرقم المرجعي (توليد تلقائي أو يدوي)
- نوع المستند (صادر/وارد)
- التاريخ
- المرسل/المستلم
- الجهة المرتبطة بالوثيقة
- موضوع الوثيقة
- الملاحظات

### ✅ إدارة المرفقات
- رفع ملفات مرفقة (PDF, Word, Excel, صور، إلخ)
- عرض الملفات المرفقة
- حذف المرفقات
- تخزين آمن للملفات

### ✅ البحث والتصفية
- بحث سريع في جميع حقول المستند
- تصفية حسب نوع المستند
- تصفية حسب التاريخ
- عرض النتائج في جدول منظم

### ✅ التصدير والتقارير
- تصدير البيانات إلى Excel
- إحصائيات المستندات
- تقارير شهرية وسنوية

## متطلبات النظام

- Windows 10 أو أحدث
- .NET 6.0 Runtime
- 100 ميجابايت مساحة فارغة على القرص الصلب

## التثبيت والتشغيل

### 1. تحميل المتطلبات
```bash
# تثبيت .NET 6.0 SDK
winget install Microsoft.DotNet.SDK.6

# أو تحميل من الموقع الرسمي:
# https://dotnet.microsoft.com/download/dotnet/6.0
```

### 2. بناء المشروع
```bash
# في مجلد المشروع
dotnet restore
dotnet build
```

### 3. تشغيل التطبيق
```bash
dotnet run
```

أو بناء ملف تنفيذي:
```bash
dotnet publish -c Release -r win-x64 --self-contained
```

## هيكل المشروع

```
DocumentArchive/
├── Models/                 # نماذج البيانات
│   ├── Document.cs        # نموذج المستند
│   └── DocumentType.cs    # تعداد أنواع المستندات
├── Data/                  # طبقة الوصول للبيانات
│   ├── DatabaseHelper.cs  # مساعد قاعدة البيانات
│   └── DocumentRepository.cs # مستودع المستندات
├── Services/              # خدمات التطبيق
│   ├── DocumentService.cs # خدمة المستندات
│   └── FileService.cs     # خدمة إدارة الملفات
├── Forms/                 # واجهات المستخدم
│   ├── MainForm.cs        # الشاشة الرئيسية
│   └── AddEditDocumentForm.cs # شاشة إضافة/تعديل
├── Program.cs             # نقطة البداية
├── App.config             # إعدادات التطبيق
└── DocumentArchive.csproj # ملف المشروع
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية تُحفظ في:
```
%APPDATA%\DocumentArchive\documents.db
```

### جداول قاعدة البيانات

#### جدول Documents
- Id (المعرف الفريد)
- ReferenceNumber (الرقم المرجعي)
- Type (نوع المستند)
- DocumentDate (تاريخ المستند)
- SenderReceiver (المرسل/المستلم)
- RelatedEntity (الجهة المرتبطة)
- Subject (الموضوع)
- Notes (الملاحظات)
- AttachmentPath (مسار الملف المرفق)
- OriginalFileName (اسم الملف الأصلي)
- CreatedDate (تاريخ الإنشاء)
- ModifiedDate (تاريخ التعديل)

#### جدول Settings
- Key (المفتاح)
- Value (القيمة)

## المرفقات

تُحفظ الملفات المرفقة في:
```
%APPDATA%\DocumentArchive\Attachments\
```

### أنواع الملفات المدعومة
- مستندات: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF
- صور: JPG, JPEG, PNG, GIF, BMP, TIFF
- أرشيف: ZIP, RAR, 7Z

## الاستخدام

### 1. إضافة مستند جديد
- انقر على "مستند جديد" في شريط الأدوات
- املأ البيانات المطلوبة
- أرفق ملف إذا لزم الأمر
- انقر "حفظ"

### 2. البحث في المستندات
- استخدم مربع البحث في الأعلى
- اختر نوع المستند من القائمة المنسدلة
- النتائج تظهر فوراً أثناء الكتابة

### 3. تعديل مستند
- انقر مرتين على المستند في الجدول
- أو اختر المستند وانقر "تعديل"

### 4. تصدير البيانات
- انقر "تصدير إلى Excel" في شريط الأدوات
- اختر مكان الحفظ
- سيتم إنشاء ملف Excel بجميع البيانات

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## إصدارات مستقبلية

### المميزات المخططة
- [ ] نسخ احتياطية تلقائية
- [ ] تصدير إلى PDF
- [ ] طباعة التقارير
- [ ] إعدادات متقدمة للترقيم
- [ ] دعم المرفقات المتعددة
- [ ] نظام المستخدمين والصلاحيات
- [ ] واجهة ويب اختيارية

---

**تم تطوير هذا النظام ليكون بسيطاً وفعالاً لإدارة أرشيف المستندات في المؤسسات الصغيرة والمتوسطة.**
