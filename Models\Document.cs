namespace DocumentArchive.Models
{
    /// <summary>
    /// نموذج المستند
    /// </summary>
    public class Document
    {
        /// <summary>
        /// المعرف الفريد للمستند
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// الرقم المرجعي للمستند
        /// </summary>
        public string ReferenceNumber { get; set; } = string.Empty;

        /// <summary>
        /// نوع المستند (وارد/صادر)
        /// </summary>
        public DocumentType Type { get; set; }

        /// <summary>
        /// تاريخ المستند
        /// </summary>
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// المرسل أو المستلم
        /// </summary>
        public string SenderReceiver { get; set; } = string.Empty;

        /// <summary>
        /// الجهة المرتبطة بالوثيقة
        /// </summary>
        public string RelatedEntity { get; set; } = string.Empty;

        /// <summary>
        /// موضوع الوثيقة
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// الملاحظات
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// مسار الملف المرفق
        /// </summary>
        public string? AttachmentPath { get; set; }

        /// <summary>
        /// اسم الملف المرفق الأصلي
        /// </summary>
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// التحقق من وجود ملف مرفق
        /// </summary>
        public bool HasAttachment => !string.IsNullOrEmpty(AttachmentPath);

        /// <summary>
        /// الحصول على نص نوع المستند
        /// </summary>
        public string TypeDisplayText => DocumentTypeHelper.GetDisplayText(Type);

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Document()
        {
            DocumentDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }
    }
}
