using DocumentArchive.Data;
using DocumentArchive.Models;
using DocumentArchive.Services;

namespace DocumentArchive.Forms
{
    public partial class MainForm : Form
    {
        private readonly DocumentRepository _repository;
        private readonly DocumentService _documentService;
        private List<Document> _currentDocuments;

        public MainForm()
        {
            _repository = new DocumentRepository();
            _documentService = new DocumentService();
            _currentDocuments = new List<Document>();

            SetupForm();
            LoadDocuments();
        }

        private void SetupForm()
        {
            Text = "نظام أرشفة المستندات";
            Size = new Size(1200, 700);
            StartPosition = FormStartPosition.CenterScreen;
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;

            // إعداد شريط الأدوات
            SetupToolbar();
            
            // إعداد شريط الحالة
            SetupStatusBar();
            
            // إعداد جدول البيانات
            SetupDataGridView();
            
            // إعداد شريط البحث
            SetupSearchPanel();
        }

        private void SetupToolbar()
        {
            var toolbar = new ToolStrip
            {
                Dock = DockStyle.Top,
                RightToLeft = RightToLeft.Yes
            };

            var btnNew = new ToolStripButton("مستند جديد")
            {
                Image = CreateSimpleIcon(Color.Green),
                ImageTransparentColor = Color.Magenta
            };
            btnNew.Click += BtnNew_Click;

            var btnEdit = new ToolStripButton("تعديل")
            {
                Image = CreateSimpleIcon(Color.Blue),
                ImageTransparentColor = Color.Magenta
            };
            btnEdit.Click += BtnEdit_Click;

            var btnDelete = new ToolStripButton("حذف")
            {
                Image = CreateSimpleIcon(Color.Red),
                ImageTransparentColor = Color.Magenta
            };
            btnDelete.Click += BtnDelete_Click;

            var btnRefresh = new ToolStripButton("تحديث")
            {
                Image = CreateSimpleIcon(Color.Orange),
                ImageTransparentColor = Color.Magenta
            };
            btnRefresh.Click += BtnRefresh_Click;

            var btnExport = new ToolStripButton("تصدير إلى Excel")
            {
                Image = CreateSimpleIcon(Color.DarkGreen),
                ImageTransparentColor = Color.Magenta
            };
            btnExport.Click += BtnExport_Click;

            toolbar.Items.AddRange(new ToolStripItem[]
            {
                btnNew, new ToolStripSeparator(),
                btnEdit, btnDelete, new ToolStripSeparator(),
                btnRefresh, new ToolStripSeparator(),
                btnExport
            });

            Controls.Add(toolbar);
        }

        private void SetupStatusBar()
        {
            var statusBar = new StatusStrip
            {
                RightToLeft = RightToLeft.Yes
            };

            var lblStatus = new ToolStripStatusLabel("جاهز")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblCount = new ToolStripStatusLabel("عدد المستندات: 0");

            statusBar.Items.AddRange(new ToolStripItem[] { lblStatus, lblCount });
            Controls.Add(statusBar);
        }

        private void SetupSearchPanel()
        {
            var searchPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                Padding = new Padding(10)
            };

            var txtSearch = new TextBox
            {
                Name = "txtSearch",
                Width = 200,
                Location = new Point(10, 15),
                PlaceholderText = "البحث في المستندات..."
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            var cmbType = new ComboBox
            {
                Name = "cmbType",
                Width = 100,
                Location = new Point(220, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbType.Items.Add("الكل");
            cmbType.Items.Add("وارد");
            cmbType.Items.Add("صادر");
            cmbType.SelectedIndex = 0;
            cmbType.SelectedIndexChanged += CmbType_SelectedIndexChanged;

            var btnSearch = new Button
            {
                Text = "بحث",
                Width = 80,
                Location = new Point(330, 14),
                Height = 23
            };
            btnSearch.Click += BtnSearch_Click;

            var btnClearSearch = new Button
            {
                Text = "مسح",
                Width = 60,
                Location = new Point(420, 14),
                Height = 23
            };
            btnClearSearch.Click += BtnClearSearch_Click;

            searchPanel.Controls.AddRange(new Control[] { txtSearch, cmbType, btnSearch, btnClearSearch });
            Controls.Add(searchPanel);
        }

        private void SetupDataGridView()
        {
            var dgv = new DataGridView
            {
                Name = "dgvDocuments",
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RightToLeft = RightToLeft.Yes
            };

            // إعداد الأعمدة
            dgv.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "ReferenceNumber",
                    HeaderText = "الرقم المرجعي",
                    DataPropertyName = "ReferenceNumber",
                    Width = 150
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Type",
                    HeaderText = "النوع",
                    DataPropertyName = "TypeDisplayText",
                    Width = 80
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DocumentDate",
                    HeaderText = "التاريخ",
                    DataPropertyName = "DocumentDate",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "SenderReceiver",
                    HeaderText = "المرسل/المستلم",
                    DataPropertyName = "SenderReceiver",
                    Width = 150
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Subject",
                    HeaderText = "الموضوع",
                    DataPropertyName = "Subject",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "RelatedEntity",
                    HeaderText = "الجهة المرتبطة",
                    DataPropertyName = "RelatedEntity",
                    Width = 150
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "HasAttachment",
                    HeaderText = "مرفق",
                    DataPropertyName = "HasAttachment",
                    Width = 60
                }
            });

            dgv.DoubleClick += DgvDocuments_DoubleClick;
            dgv.KeyDown += DgvDocuments_KeyDown;

            Controls.Add(dgv);
        }

        private async void LoadDocuments()
        {
            try
            {
                _currentDocuments = await _repository.GetAllDocumentsAsync();
                RefreshDataGridView();
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستندات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            var dgv = Controls.Find("dgvDocuments", true).FirstOrDefault() as DataGridView;
            if (dgv != null)
            {
                dgv.DataSource = null;
                dgv.DataSource = _currentDocuments;
            }
        }

        private void UpdateStatusBar()
        {
            var statusBar = Controls.OfType<StatusStrip>().FirstOrDefault();
            if (statusBar?.Items.Count > 1)
            {
                ((ToolStripStatusLabel)statusBar.Items[1]).Text = $"عدد المستندات: {_currentDocuments.Count}";
            }
        }

        private Bitmap CreateSimpleIcon(Color color)
        {
            var bitmap = new Bitmap(16, 16);
            using var g = Graphics.FromImage(bitmap);
            using var brush = new SolidBrush(color);
            g.FillRectangle(brush, 2, 2, 12, 12);
            return bitmap;
        }

        // معالجات الأحداث
        private void BtnNew_Click(object? sender, EventArgs e)
        {
            var form = new AddEditDocumentForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadDocuments();
            }
        }

        private void BtnEdit_Click(object? sender, EventArgs e)
        {
            var dgv = Controls.Find("dgvDocuments", true).FirstOrDefault() as DataGridView;
            if (dgv?.SelectedRows.Count > 0)
            {
                var document = dgv.SelectedRows[0].DataBoundItem as Document;
                if (document != null)
                {
                    var form = new AddEditDocumentForm(document);
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        LoadDocuments();
                    }
                }
            }
        }

        private async void BtnDelete_Click(object? sender, EventArgs e)
        {
            var dgv = Controls.Find("dgvDocuments", true).FirstOrDefault() as DataGridView;
            if (dgv?.SelectedRows.Count > 0)
            {
                var document = dgv.SelectedRows[0].DataBoundItem as Document;
                if (document != null)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا المستند؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        try
                        {
                            await _documentService.DeleteDocumentAsync(document.Id);
                            LoadDocuments();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف المستند: {ex.Message}", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }

        private void BtnRefresh_Click(object? sender, EventArgs e)
        {
            LoadDocuments();
        }

        private async void BtnExport_Click(object? sender, EventArgs e)
        {
            using var saveDialog = new SaveFileDialog
            {
                Filter = "Excel Files|*.xlsx",
                Title = "حفظ ملف Excel",
                FileName = $"المستندات_{DateTime.Now:yyyy-MM-dd}.xlsx"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    await _documentService.ExportToExcelAsync(_currentDocuments, saveDialog.FileName);
                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void TxtSearch_TextChanged(object? sender, EventArgs e)
        {
            await PerformSearch();
        }

        private async void CmbType_SelectedIndexChanged(object? sender, EventArgs e)
        {
            await PerformSearch();
        }

        private async void BtnSearch_Click(object? sender, EventArgs e)
        {
            await PerformSearch();
        }

        private void BtnClearSearch_Click(object? sender, EventArgs e)
        {
            var txtSearch = Controls.Find("txtSearch", true).FirstOrDefault() as TextBox;
            var cmbType = Controls.Find("cmbType", true).FirstOrDefault() as ComboBox;
            
            if (txtSearch != null) txtSearch.Clear();
            if (cmbType != null) cmbType.SelectedIndex = 0;
            
            LoadDocuments();
        }

        private async Task PerformSearch()
        {
            try
            {
                var txtSearch = Controls.Find("txtSearch", true).FirstOrDefault() as TextBox;
                var cmbType = Controls.Find("cmbType", true).FirstOrDefault() as ComboBox;

                var searchTerm = txtSearch?.Text;
                DocumentType? type = null;

                if (cmbType?.SelectedIndex == 1) type = DocumentType.Incoming;
                else if (cmbType?.SelectedIndex == 2) type = DocumentType.Outgoing;

                _currentDocuments = await _repository.SearchDocumentsAsync(searchTerm, type);
                RefreshDataGridView();
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvDocuments_DoubleClick(object? sender, EventArgs e)
        {
            BtnEdit_Click(sender, e);
        }

        private void DgvDocuments_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete)
            {
                BtnDelete_Click(sender, e);
            }
            else if (e.KeyCode == Keys.Enter)
            {
                BtnEdit_Click(sender, e);
            }
        }
    }
}
