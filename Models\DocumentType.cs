namespace DocumentArchive.Models
{
    /// <summary>
    /// تعداد أنواع المستندات
    /// </summary>
    public enum DocumentType
    {
        /// <summary>
        /// مستند وارد
        /// </summary>
        Incoming = 1,

        /// <summary>
        /// مستند صادر
        /// </summary>
        Outgoing = 2
    }

    /// <summary>
    /// فئة مساعدة لأنواع المستندات
    /// </summary>
    public static class DocumentTypeHelper
    {
        /// <summary>
        /// الحصول على النص العربي لنوع المستند
        /// </summary>
        /// <param name="type">نوع المستند</param>
        /// <returns>النص العربي</returns>
        public static string GetDisplayText(DocumentType type)
        {
            return type switch
            {
                DocumentType.Incoming => "وارد",
                DocumentType.Outgoing => "صادر",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على جميع أنواع المستندات
        /// </summary>
        /// <returns>قائمة بأنواع المستندات</returns>
        public static Dictionary<DocumentType, string> GetAllTypes()
        {
            return new Dictionary<DocumentType, string>
            {
                { DocumentType.Incoming, "وارد" },
                { DocumentType.Outgoing, "صادر" }
            };
        }
    }
}
