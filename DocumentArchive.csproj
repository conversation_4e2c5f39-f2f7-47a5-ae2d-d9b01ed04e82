<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>نظام أرشفة المستندات</AssemblyTitle>
    <AssemblyDescription>نظام بسيط لأرشفة المستندات الصادرة والواردة</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="EPPlus" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Forms\" />
    <Folder Include="Models\" />
    <Folder Include="Data\" />
    <Folder Include="Services\" />
    <Folder Include="Resources\" />
    <Folder Include="Attachments\" />
  </ItemGroup>

</Project>
