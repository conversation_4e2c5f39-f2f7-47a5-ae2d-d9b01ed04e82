using System.Data.SQLite;
using DocumentArchive.Models;

namespace DocumentArchive.Data
{
    /// <summary>
    /// مساعد قاعدة البيانات
    /// </summary>
    public static class DatabaseHelper
    {
        private static readonly string DatabasePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DocumentArchive",
            "documents.db"
        );

        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        public static string ConnectionString => $"Data Source={DatabasePath};Version=3;";

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// </summary>
        public static void InitializeDatabase()
        {
            try
            {
                // إنشاء مجلد التطبيق إذا لم يكن موجوداً
                var appDataDir = Path.GetDirectoryName(DatabasePath);
                if (!Directory.Exists(appDataDir))
                {
                    Directory.CreateDirectory(appDataDir!);
                }

                // إنشاء قاعدة البيانات والجداول
                using var connection = new SQLiteConnection(ConnectionString);
                connection.Open();

                var createTableCommand = @"
                    CREATE TABLE IF NOT EXISTS Documents (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ReferenceNumber TEXT NOT NULL,
                        Type INTEGER NOT NULL,
                        DocumentDate TEXT NOT NULL,
                        SenderReceiver TEXT NOT NULL,
                        RelatedEntity TEXT NOT NULL,
                        Subject TEXT NOT NULL,
                        Notes TEXT,
                        AttachmentPath TEXT,
                        OriginalFileName TEXT,
                        CreatedDate TEXT NOT NULL,
                        ModifiedDate TEXT NOT NULL
                    );

                    CREATE INDEX IF NOT EXISTS idx_reference_number ON Documents(ReferenceNumber);
                    CREATE INDEX IF NOT EXISTS idx_type ON Documents(Type);
                    CREATE INDEX IF NOT EXISTS idx_document_date ON Documents(DocumentDate);
                    CREATE INDEX IF NOT EXISTS idx_sender_receiver ON Documents(SenderReceiver);
                    CREATE INDEX IF NOT EXISTS idx_subject ON Documents(Subject);
                ";

                using var command = new SQLiteCommand(createTableCommand, connection);
                command.ExecuteNonQuery();

                // إنشاء جدول الإعدادات
                var createSettingsCommand = @"
                    CREATE TABLE IF NOT EXISTS Settings (
                        Key TEXT PRIMARY KEY,
                        Value TEXT NOT NULL
                    );

                    INSERT OR IGNORE INTO Settings (Key, Value) VALUES ('NextIncomingNumber', '1');
                    INSERT OR IGNORE INTO Settings (Key, Value) VALUES ('NextOutgoingNumber', '1');
                    INSERT OR IGNORE INTO Settings (Key, Value) VALUES ('AttachmentsPath', '');
                ";

                using var settingsCommand = new SQLiteCommand(createSettingsCommand, connection);
                settingsCommand.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على الرقم التالي للمستند
        /// </summary>
        /// <param name="type">نوع المستند</param>
        /// <returns>الرقم التالي</returns>
        public static int GetNextDocumentNumber(DocumentType type)
        {
            var key = type == DocumentType.Incoming ? "NextIncomingNumber" : "NextOutgoingNumber";
            
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            using var command = new SQLiteCommand("SELECT Value FROM Settings WHERE Key = @key", connection);
            command.Parameters.AddWithValue("@key", key);
            
            var result = command.ExecuteScalar()?.ToString();
            return int.TryParse(result, out var number) ? number : 1;
        }

        /// <summary>
        /// تحديث الرقم التالي للمستند
        /// </summary>
        /// <param name="type">نوع المستند</param>
        /// <param name="nextNumber">الرقم التالي</param>
        public static void UpdateNextDocumentNumber(DocumentType type, int nextNumber)
        {
            var key = type == DocumentType.Incoming ? "NextIncomingNumber" : "NextOutgoingNumber";
            
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();

            using var command = new SQLiteCommand("UPDATE Settings SET Value = @value WHERE Key = @key", connection);
            command.Parameters.AddWithValue("@key", key);
            command.Parameters.AddWithValue("@value", nextNumber.ToString());
            command.ExecuteNonQuery();
        }
    }
}
